<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>vn.osp</groupId>
    <artifactId>demo-common</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>demo-common</name>
    <description>demo-common</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>21</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!-- Loại bỏ Logback -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>vn.osp</groupId>
            <artifactId>common</artifactId>
            <version>1.0.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-actuator-autoconfigure</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; Micrometer Tracing (kết hợp với OTel) &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>io.micrometer</groupId>-->
<!--            <artifactId>micrometer-tracing-bridge-otel</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; OpenTelemetry Exporter (gửi dữ liệu ra Collector, ví dụ SigNoz, Jaeger, Tempo) &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>io.opentelemetry</groupId>-->
<!--            <artifactId>opentelemetry-exporter-otlp</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; Publish metric&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>io.micrometer</groupId>-->
<!--            <artifactId>micrometer-registry-otlp</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; Compatible Log4j appender version &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>io.opentelemetry.instrumentation</groupId>-->
<!--            <artifactId>opentelemetry-log4j-appender-2.17</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
