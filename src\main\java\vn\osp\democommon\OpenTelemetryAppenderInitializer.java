package vn.osp.democommon;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.instrumentation.log4j.appender.v2_17.OpenTelemetryAppender;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

@Component
class OpenTelemetryAppenderInitializer implements InitializingBean {

    private final OpenTelemetry openTelemetry;

    OpenTelemetryAppenderInitializer(OpenTelemetry openTelemetry) {
        this.openTelemetry = openTelemetry;
    }

    @Override
    public void afterPropertiesSet() {
        try {
            OpenTelemetryAppender.install(this.openTelemetry);
        } catch (Exception e) {
            // Log error but don't fail application startup
            System.err.println("Failed to install OpenTelemetry appender: " + e.getMessage());
        }
    }

}